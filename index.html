<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Document</title>

    <script src="https://cdn.jsdelivr.net/npm/@tailwindcss/browser@4"></script>

    <style>
        .mar{
            background: url(./mb.png) no-repeat center center;
            background-size: cover;
            transition: all 0.3s ease-in-out;
            transform-origin: center center;
            box-sizing: border-box;
        }

        .mar.clicked {
            background: url(./ma.png) no-repeat center center;
            background-size: cover;
            transform: scale(1.2);
            transform-origin: center center;
            box-sizing: border-box;
        }
    </style>

    <script>
        // script to play sound after click of button and animate
        function playSound() {
            var audio = new Audio('./margaret.wav');
            

            // Get the div element
            var div = document.querySelector('.mar');

            // Add the clicked class for animation and image change
            div.classList.add('clicked');
            audio.play();

            // Remove the class after animation completes to reset
            setTimeout(function() {
                div.classList.remove('clicked');
            }, 400); // Match the transition duration
        }
    </script>
</head>
<body class="h-screen w-screen bg-neutral-900 flex items-center justify-center">
    <div class="rounded-full p-4 h-64 w-64 bg-black flex justify-center items-center mar shadow shadow-white" onclick="playSound()">
    </div>
</body>
</html>