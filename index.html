<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Document</title>

    <script src="https://cdn.jsdelivr.net/npm/@tailwindcss/browser@4"></script>

    <style>
        .mar{
            background: url(./ma.png) no-repeat center center;
            background-size: cover;
        }
    </style>

    <script>
        // script to play sound after click of button
        function playSound() {
            var audio = new Audio('./margaret.wav');
            audio.play();
        }
    </script>
</head>
<body class="h-screen w-screen bg-neutral-900 flex items-center justify-center">
    <div class="rounded-full p-4 h-64 w-64 bg-black flex justify-center items-center mar shadow shadow-white" onclick="playSound()">
    </div>
</body>
</html>